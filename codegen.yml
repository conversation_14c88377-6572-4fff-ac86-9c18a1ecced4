# codegen.yml

# 1. Especifica tu esquema GraphQL:
#    Puede ser una URL o un archivo local.
#    ¡¡ASEGÚRATE DE CAMBIAR ESTO A TU VALOR REAL!!
schema: https://beta-api-rmo.rosclar.com/graphql/ # Ejemplo: 'http://localhost:4000/graphql' o la URL de tu API
# o si tienes el schema en un archivo (ej. después de una introspección):
# schema: ./graphql/schemas/schema.json # o schema.graphql

# 2. Dónde encontrar tus archivos de operaciones (.graphql):
#    Esto le dice a Codegen que busque todos los archivos .graphql
#    dentro de la carpeta graphql/operations y sus subcarpetas.
documents: "./graphql/operations/**/*.graphql"

# 3. Configuración de la generación de código:
generates:
  # Ruta al archivo de salida que quieres generar.
  # Esto coincide con tu estructura: graphql/schemas/generated.ts
  ./graphql/schemas/generated.ts:
    plugins:
      # Plugin para generar tipos TypeScript básicos de tu esquema.
      - "typescript"
      # Plugin para generar tipos TypeScript para tus queries, mutaciones, etc.
      - "typescript-operations"
      # (Opcional - Ejemplo para React Apollo) Plugin para generar hooks.
      # Si no usas React Apollo, puedes quitar esta línea o cambiarla.
      - "typescript-react-apollo"
    config:
      withHooks: true # (Solo si usas typescript-react-apollo)
      # Aquí puedes configurar más cosas, como mapear escalares personalizados:
      # scalars:
      #   Date: string # O el tipo que prefieras para las fechas, ej: Date
      #   JSONString: string # O: Record<string, any>
      #   DateTime: string # O: Date