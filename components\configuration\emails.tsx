"use client";

import React, {
  useState,
  useRef,
  useMemo,
  useEffect,
  useCallback,
} from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Pagination,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Textarea,
  Card,
  Spinner,
  Checkbox,
  SelectItem,
  Select,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { FilterDropdown } from "@/components/projects/projects-table/filter-dropdown";
import {
  useAllEmailTemplatesQuery,
  useExecuteCreateEmailTemplateMutation,
  useExecuteUpdateEmailTemplateMutation,
  useExecuteDeleteEmailTemplateMutation,
  EmailTemplateType,
} from "@/graphql/schemas/generated";
import { useRowCountStore } from "@/store/use-row-count-store";
import { useEmailTopics } from "@/hooks/emails/useEmailTopic";

// Component for the Email Management
interface EmailsModalProps {
  isCreating: boolean;
  setIsCreating: (isCreating: boolean) => void;
  canEditConfiguration: boolean;
}

export default function Emails({
  isCreating,
  setIsCreating,
  canEditConfiguration,
}: EmailsModalProps) {
  const { topics, loading: topicsLoading, fetchEmailTopics } = useEmailTopics();

  const { rowCount } = useRowCountStore();
  const [page, setPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);
  const [filteredData, setFilteredData] = useState<EmailTemplateType[]>([]);

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // GraphQL hooks
  const { data, loading, error } = useAllEmailTemplatesQuery();
  const [createEmailTemplate, { loading: createLoading }] =
    useExecuteCreateEmailTemplateMutation();
  const [updateEmailTemplate, { loading: updateLoading }] =
    useExecuteUpdateEmailTemplateMutation();
  const [deleteEmailTemplate, { loading: deleteLoading }] =
    useExecuteDeleteEmailTemplateMutation();

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedEmail, setSelectedEmail] = useState<EmailTemplateType | null>(
    null,
  );

  // Form states for create/edit
  const [newEmail, setNewEmail] = useState<Partial<EmailTemplateType>>({
    name: "",
    subject: "",
    body: "",
    topic: "",
    isExternal: false,
  });

  const emailTemplates = data?.allEmailTemplates?.filter(Boolean) || [];

  // Helper functions for filtering and sorting
  const getUniqueValues = (column: string) => {
    return Array.from(
      new Set(
        emailTemplates.map((email) => {
          if (!email) return;

          // Safe property access with type checking
          const value = column in email ? (email as any)[column] : undefined;

          if (value === null || value === undefined) return "Sin valor";

          return String(value);
        }),
      ),
    );
  };

  const handleFilterChange = (column: string, selectedValues: string[]) => {
    const newFilters = {
      ...activeFilters,
      [column]: selectedValues,
    };

    if (selectedValues.length === 0) {
      delete newFilters[column];
    }

    setActiveFilters(newFilters);
    setPage(1); // Reset to first page when filtering
  };

  const handleSort = (column: string, direction: "asc" | "desc") => {
    setSortConfig({ column, direction });
  };

  const applyFiltersAndSort = useCallback(
    (
      term: string,
      filters: Record<string, string[]>,
      sort: { column: string; direction: "asc" | "desc" } | null,
    ) => {
      // Filter out null values and ensure we're working with EmailTemplateType[]
      let filtered = emailTemplates.filter(
        (email): email is EmailTemplateType => email !== null,
      );

      // Apply search term filter
      if (term) {
        filtered = filtered.filter(
          (email) =>
            email.name.toLowerCase().includes(term.toLowerCase()) ||
            email.subject.toLowerCase().includes(term.toLowerCase()) ||
            (email.topic &&
              email.topic.toLowerCase().includes(term.toLowerCase())) ||
            email.body.toLowerCase().includes(term.toLowerCase()),
        );
      }

      // Apply column filters
      Object.entries(filters).forEach(([column, values]) => {
        if (values.length > 0) {
          filtered = filtered.filter((email) => {
            const value = email[column as keyof EmailTemplateType];
            const stringValue =
              value === null || value === undefined
                ? "Sin valor"
                : String(value);

            return values.includes(stringValue);
          });
        }
      });

      // Apply sorting
      if (sort) {
        filtered.sort((a, b) => {
          const aValue = a[sort.column as keyof EmailTemplateType];
          const bValue = b[sort.column as keyof EmailTemplateType];

          const aString = String(aValue || "");
          const bString = String(bValue || "");

          const comparison = aString.localeCompare(bString);

          return sort.direction === "asc" ? comparison : -comparison;
        });
      }

      setFilteredData(filtered);
    },
    [emailTemplates],
  );

  // Effect to apply filters and sort when emailTemplates data changes
  useEffect(() => {
    if (emailTemplates.length > 0) {
      applyFiltersAndSort(searchTerm, activeFilters, sortConfig);
    }
  }, [emailTemplates]);

  // Effect to apply filters and sort when search, filters, or sort config changes
  useEffect(() => {
    applyFiltersAndSort(searchTerm, activeFilters, sortConfig);
  }, [searchTerm, activeFilters, sortConfig]);

  const pages = Math.ceil(filteredData.length / rowCount);
  const items = useMemo(() => {
    const start = (page - 1) * rowCount;
    const end = start + rowCount;

    return filteredData.slice(start, end);
  }, [page, filteredData]);

  const handleRowClick = (email: EmailTemplateType) => {
    setSelectedEmail(email);
    setIsViewModalOpen(true);
  };

  const handleEdit = (email: EmailTemplateType) => {
    setSelectedEmail(email);
    setNewEmail({
      name: email.name,
      subject: email.subject,
      body: email.body,
      topic: email.topic || "",
      isExternal: email.isExternal,
    });
    setIsEditModalOpen(true);
  };

  const handleDelete = (email: EmailTemplateType) => {
    setSelectedEmail(email);
    setIsDeleteModalOpen(true);
  };

  useEffect(() => {
    fetchEmailTopics();
  }, []);

  useEffect(() => {
    if (isCreating) {
      setNewEmail({
        name: "",
        subject: "",
        body: "",
        topic: "",
      });
      setIsCreateModalOpen(true);
    } else {
      setIsCreateModalOpen(false);
    }
  }, [isCreating]);

  const handleSaveCreate = async () => {
    if (
      !newEmail.name ||
      !newEmail.subject ||
      !newEmail.body ||
      !newEmail.topic
    ) {
      return;
    }

    try {
      await createEmailTemplate({
        variables: {
          name: newEmail.name,
          subject: newEmail.subject,
          body: newEmail.body,
          topic: newEmail.topic,
          isExternal: newEmail.isExternal,
        },
        refetchQueries: ["AllEmailTemplates"],
      });

      setIsCreateModalOpen(false);
      setIsCreating(false);
      setNewEmail({
        name: "",
        subject: "",
        body: "",
        topic: "",
        isExternal: false,
      });
    } catch (error) {
      console.error("Error creating email template:", error);
    }
  };

  const handleSaveEdit = async () => {
    if (
      !selectedEmail ||
      !newEmail.name ||
      !newEmail.subject ||
      !newEmail.body ||
      !newEmail.topic
    ) {
      return;
    }

    try {
      await updateEmailTemplate({
        variables: {
          id: selectedEmail.id,
          name: newEmail.name,
          subject: newEmail.subject,
          body: newEmail.body,
          topic: newEmail.topic,
          isExternal: newEmail.isExternal,
        },
        refetchQueries: ["AllEmailTemplates"],
      });

      setIsEditModalOpen(false);
      setSelectedEmail(null);
      setNewEmail({
        name: "",
        subject: "",
        body: "",
        topic: "",
        isExternal: false,
      });
    } catch (error) {
      console.error("Error updating email template:", error);
    }
  };

  const handleConfirmDelete = async () => {
    if (!selectedEmail) return;

    try {
      await deleteEmailTemplate({
        variables: {
          id: selectedEmail.id,
        },
        refetchQueries: ["AllEmailTemplates"],
      });

      setIsDeleteModalOpen(false);
      setSelectedEmail(null);
    } catch (error) {
      console.error("Error deleting email template:", error);
    }
  };

  // Search and filter handlers
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(1); // Reset to first page when searching
  };

  // Simple textarea input handler
  const handleTextareaInput = (value: string) => {
    setNewEmail({ ...newEmail, body: value });
  };

  // Function to highlight variables in text
  const highlightVariables = (text: string) => {
    if (!text) return text;

    // Define the variables with their specific colors
    const variableColors = {
      "@lid": { color: "#dc2626", bg: "rgba(220, 38, 38, 0.1)" }, // Red
      "@razonsocial": { color: "#ea580c", bg: "rgba(234, 88, 12, 0.1)" }, // Orange
      "@agregador": { color: "#ca8a04", bg: "rgba(202, 138, 4, 0.1)" }, // Yellow
      "@tipologia": { color: "#16a34a", bg: "rgba(22, 163, 74, 0.1)" }, // Green
      "@meslive": { color: "#0891b2", bg: "rgba(8, 145, 178, 0.1)" }, // Cyan
      "@testlive": { color: "#0066cc", bg: "rgba(0, 102, 204, 0.1)" }, // Blue
      "@coordinador": { color: "#7c3aed", bg: "rgba(124, 58, 237, 0.1)" }, // Purple
      "@implementador1": { color: "#c026d3", bg: "rgba(192, 38, 211, 0.1)" }, // Magenta
      "@implementador2": { color: "#db2777", bg: "rgba(219, 39, 119, 0.1)" }, // Pink
      "@incubadora": { color: "#059669", bg: "rgba(5, 150, 105, 0.1)" }, // Emerald
      "@backup": { color: "#7c2d12", bg: "rgba(124, 45, 18, 0.1)" }, // Brown
    };

    let highlightedText = text;

    Object.entries(variableColors).forEach(([variable, colors]) => {
      const regex = new RegExp(`(${variable.replace("@", "\\@")})`, "gi");

      highlightedText = highlightedText.replace(
        regex,
        `<span style="color: ${colors.color}; font-weight: 600; background-color: ${colors.bg}; padding: 1px 3px; border-radius: 3px;">$1</span>`,
      );
    });

    return highlightedText;
  };

  const closeCreateEditModal = () => {
    setIsCreateModalOpen(false);
    setIsEditModalOpen(false);
    setIsCreating(false);
    setSelectedEmail(null);
    setNewEmail({
      name: "",
      subject: "",
      body: "",
      topic: "",
      isExternal: false,
    });
  };

  if (loading) {
    return (
      <div className="pt-4 w-full flex justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="pt-4 w-full">
        <p className="text-danger">
          Error loading email templates: {error.message}
        </p>
      </div>
    );
  }

  return (
    <div className="pt-4 w-full">
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-4">
        <div className="flex gap-2 w-full">
          <Input
            isClearable
            className="w-full"
            placeholder="Buscar por nombre, asunto, tema o contenido..."
            startContent={
              <Icon
                className="text-default-400"
                icon="lucide:search"
                width={18}
              />
            }
            value={searchTerm}
            onClear={() => setSearchTerm("")}
            onValueChange={handleSearch}
          />
        </div>
        {/* <div className="flex gap-2 w-full sm:w-auto justify-end">
          <Button
            color={
              searchTerm || Object.keys(activeFilters).length > 0
                ? "primary"
                : "default"
            }
            variant="flat"
            onPress={handleClearFilters}
          >
            Borrar filtros{" "}
            {(searchTerm || Object.keys(activeFilters).length > 0) &&
              `(${(searchTerm ? 1 : 0) + Object.keys(activeFilters).length})`}
          </Button>
        </div> */}
      </div>

      <Table
        removeWrapper
        aria-label="Tabla de emails"
        bottomContent={
          pages > 0 ? (
            <div className="flex w-full justify-center">
              <Pagination
                isCompact
                showControls
                showShadow
                color="primary"
                page={page}
                total={pages}
                onChange={(newPage) => setPage(newPage)}
              />
            </div>
          ) : null
        }
      >
        <TableHeader>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="id"
              items={getUniqueValues("id").filter(Boolean) as string[]}
              sortConfig={sortConfig}
              title="#"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="name"
              items={getUniqueValues("name").filter(Boolean) as string[]}
              sortConfig={sortConfig}
              title="Nombre"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="subject"
              items={getUniqueValues("subject").filter(Boolean) as string[]}
              sortConfig={sortConfig}
              title="Asunto"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn className="cursor-pointer">
            <FilterDropdown
              activeFilters={activeFilters}
              column="topic"
              items={getUniqueValues("topic").filter(Boolean) as string[]}
              sortConfig={sortConfig}
              title="Tema"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>Acciones</TableColumn>
        </TableHeader>
        <TableBody
          emptyContent={"No hay emails para mostrar."}
          isLoading={loading}
          items={items}
          loadingContent={<Spinner label="Cargando correos..." />}
        >
          {(item) => {
            if (!item)
              return (
                <TableRow key={1}>
                  <TableCell>a</TableCell>
                </TableRow>
              );

            return (
              <TableRow
                key={item.id}
                className="cursor-pointer hover:bg-default-100"
                onClick={() => handleRowClick(item)}
              >
                <TableCell>{item.id}</TableCell>
                <TableCell>{item.name}</TableCell>
                <TableCell>{item.subject}</TableCell>
                <TableCell>
                  {topics.find(
                    (topic) =>
                      topic.id === (item.topic ? parseInt(item.topic) : -1),
                  )?.name || "Sin tópico"}
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      isIconOnly
                      color="primary"
                      isDisabled={!canEditConfiguration}
                      size="sm"
                      variant="flat"
                      onPress={(_) => {
                        handleEdit(item);
                      }}
                    >
                      <Icon className="text-lg" icon="lucide:edit-3" />
                    </Button>
                    <Button
                      isIconOnly
                      color="danger"
                      isDisabled={!canEditConfiguration}
                      size="sm"
                      variant="flat"
                      onPress={(_) => {
                        handleDelete(item);
                      }}
                    >
                      <Icon className="text-lg" icon="lucide:trash" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            );
          }}
        </TableBody>
      </Table>

      {/* View Email Modal - Enhanced with preview */}
      <Modal
        classNames={{
          base: "max-h-[90vh] min-w-[80vw]",
          body: "max-h-[calc(90vh-120px)] overflow-hidden p-0",
        }}
        isOpen={isViewModalOpen}
        scrollBehavior="inside"
        size="5xl"
        onClose={() => setIsViewModalOpen(false)}
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">Vista Previa del Email</h3>
          </ModalHeader>
          <ModalBody className="overflow-hidden p-6">
            {selectedEmail && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full max-h-[calc(90vh-200px)]">
                {/* Left side - Email details */}
                <div className="flex flex-col gap-4 h-full overflow-y-auto pr-2">
                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-default-500">Nombre</span>
                    <span className="text-lg font-medium">
                      {selectedEmail.name}
                    </span>
                  </div>

                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-default-500">Asunto</span>
                    <span>{selectedEmail.subject}</span>
                  </div>

                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-default-500">Tema</span>
                    <span>
                      {topics.find(
                        (topic) =>
                          topic.id ===
                          (selectedEmail.topic
                            ? parseInt(selectedEmail.topic)
                            : -1),
                      )?.name || "Sin tópico"}
                    </span>
                  </div>

                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-default-500">
                      Email Externo
                    </span>
                    <span>{selectedEmail.isExternal ? "Sí" : "No"}</span>
                  </div>

                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-default-500">
                      Contenido Original
                    </span>
                    <Card className="p-4 overflow-auto max-h-64">
                      <div
                        dangerouslySetInnerHTML={{
                          __html: highlightVariables(selectedEmail.body),
                        }}
                        className="text-sm whitespace-pre-wrap break-words"
                        style={{
                          wordWrap: "break-word",
                          overflowWrap: "break-word",
                          hyphens: "auto",
                        }}
                      />
                    </Card>
                  </div>
                </div>

                {/* Right side - Email preview */}
                <div className="flex flex-col h-full">
                  <Card className="p-0 h-full border flex flex-col">
                    <div
                      className="p-4 h-full overflow-y-scroll"
                      style={{
                        scrollbarWidth: "thin",
                        scrollbarColor: "#cbd5e0 #f7fafc",
                        maxHeight: "500px",
                        overflowY: "auto",
                      }}
                    >
                      <div className="mb-4 pb-2 border-b">
                        <div className="font-bold">
                          Asunto: @LID - @Razon Social - {selectedEmail.subject}
                        </div>
                      </div>
                      <div>
                        {/* Project card */}
                        <div
                          style={{
                            backgroundColor: "rgba(237, 239, 236, 0.2)",
                            border: "1px solid rgba(0,0,0,.125)",
                            borderRadius: "0.25rem",
                            padding: "0.8em",
                            margin: "0em 0em 1em 0em",
                            display: "flex",
                            flexWrap: "wrap",
                            fontSize: "0.85em",
                          }}
                        >
                          <div
                            style={{
                              width: "50%",
                              boxSizing: "border-box",
                              padding: "0 0.5em 0 0",
                            }}
                          >
                            <p style={{ margin: "0.2em 0" }}>
                              <strong>LID:</strong> [LID]
                            </p>
                            <p style={{ margin: "0.2em 0" }}>
                              <strong>RAZON SOCIAL:</strong> [RAZON SOCIAL]
                            </p>
                            <p style={{ margin: "0.2em 0" }}>
                              <strong>AGREGADOR:</strong> [AGREGADOR]
                            </p>
                            <p style={{ margin: "0.2em 0" }}>
                              <strong>TIPOLOGÍA:</strong> [TIPOLOGÍA]
                            </p>
                            <p style={{ margin: "0.2em 0" }}>
                              <strong>MES LIVE:</strong> [MES LIVE]
                            </p>
                            <p style={{ margin: "0.2em 0" }}>
                              <strong>MES TEST:</strong> [MES TEST]
                            </p>
                          </div>
                          <div
                            style={{
                              width: "50%",
                              boxSizing: "border-box",
                              padding: "0 0 0 0.5em",
                            }}
                          >
                            <p style={{ margin: "0.2em 0" }}>
                              <strong>COORDINADOR:</strong> [COORDINADOR]
                            </p>
                            <p style={{ margin: "0.2em 0" }}>
                              <strong>IMPLEMENTADOR 1:</strong> [IMPLEMENTADOR
                              1]
                            </p>
                            <p style={{ margin: "0.2em 0" }}>
                              <strong>IMPLEMENTADOR 2:</strong> [IMPLEMENTADOR
                              2]
                            </p>
                            <p style={{ margin: "0.2em 0" }}>
                              <strong>INCUBADORA:</strong> [INCUBADORA]
                            </p>
                            <p style={{ margin: "0.2em 0" }}>
                              <strong>BACKUP:</strong> [BACKUP]
                            </p>
                          </div>
                        </div>
                      </div>
                      <div
                        dangerouslySetInnerHTML={{
                          __html: highlightVariables(selectedEmail.body),
                        }}
                        className="email-preview text-sm whitespace-pre-wrap break-words"
                        style={{
                          wordWrap: "break-word",
                          overflowWrap: "break-word",
                          hyphens: "auto",
                        }}
                      />
                    </div>
                  </Card>
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button color="primary" onPress={() => setIsViewModalOpen(false)}>
              Cerrar
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Create/Edit Email Modal (shared structure) */}
      <Modal
        classNames={{
          base: "max-h-[90vh] min-w-[80vw]",
          body: "max-h-[calc(90vh-120px)] overflow-hidden p-0",
        }}
        isOpen={isCreateModalOpen || isEditModalOpen}
        scrollBehavior="inside"
        size="5xl"
        onClose={closeCreateEditModal}
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">
              {isEditModalOpen ? "Editar Email" : "Crear Nuevo Email"}
            </h3>
          </ModalHeader>
          <ModalBody className="overflow-hidden p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full max-h-[calc(90vh-200px)]">
              <div className="flex flex-col gap-4 h-full overflow-y-auto pr-2">
                <Input
                  label="Nombre del Email"
                  placeholder="Ej: Bienvenida Cliente"
                  value={newEmail.name}
                  onValueChange={(value) =>
                    setNewEmail({ ...newEmail, name: value })
                  }
                />

                <Input
                  label="Asunto"
                  placeholder="Ej: Bienvenido a nuestra plataforma"
                  value={newEmail.subject || ""}
                  onValueChange={(value) =>
                    setNewEmail({ ...newEmail, subject: value })
                  }
                />

                <Select
                  isLoading={topicsLoading}
                  label="Tema"
                  placeholder={
                    topicsLoading ? "Cargando temas..." : "Seleccione un tema"
                  }
                  selectedKeys={newEmail.topic ? [newEmail.topic] : []}
                  onSelectionChange={(keys) => {
                    const selectedKey = Array.from(keys)[0] as string;

                    setNewEmail({ ...newEmail, topic: selectedKey || "" });
                  }}
                >
                  {topics.map((topic) => (
                    <SelectItem
                      key={topic.id.toString()}
                      // value={topic.id.toString()}
                    >
                      {topic.name}
                    </SelectItem>
                  ))}
                </Select>

                <Checkbox
                  className=""
                  // isDisabled={true}
                  isSelected={newEmail.isExternal || false}
                  onChange={(e) =>
                    setNewEmail({
                      ...newEmail,
                      isExternal: e.target.checked,
                    })
                  }
                >
                  Email Externo
                </Checkbox>

                <div className="relative">
                  <Textarea
                    ref={textareaRef}
                    className="h-full"
                    description="Variables disponibles: @lid, @razonsocial, @agregador, @tipologia, @meslive, @testlive, @coordinador, @implementador1, @implementador2, @incubadora, @backup"
                    label="Contenido"
                    maxRows={8}
                    minRows={7}
                    placeholder="Ingrese el contenido del email."
                    value={newEmail.body}
                    onValueChange={handleTextareaInput}
                  />
                </div>
              </div>

              <div className="flex flex-col h-full">
                <Card className="p-0 h-full border flex flex-col">
                  <div
                    className="p-4 h-full overflow-y-scroll"
                    style={{
                      scrollbarWidth: "thin",
                      scrollbarColor: "#cbd5e0 #f7fafc",
                      maxHeight: "500px",
                      overflowY: "auto",
                    }}
                  >
                    <div className="mb-4 pb-2 border-b">
                      <div className="font-bold">
                        Asunto: @LID - @Razon Social -{" "}
                        {newEmail.subject || "(Sin asunto)"}
                      </div>
                    </div>
                    <div>
                      {/* Project card */}
                      <div
                        style={{
                          // Add some opacity to the bg
                          backgroundColor: "rgba(237, 239, 236, 0.2)",
                          border: "1px solid rgba(0,0,0,.125)",
                          borderRadius: "0.25rem",
                          padding: "0.8em",
                          margin: "0em 0em 1em 0em",
                          display: "flex",
                          flexWrap: "wrap",
                          fontSize: "0.85em",
                        }}
                      >
                        <div
                          style={{
                            width: "50%",
                            boxSizing: "border-box",
                            padding: "0 0.5em 0 0",
                          }}
                        >
                          <p style={{ margin: "0.2em 0" }}>
                            <strong>LID:</strong> [LID]
                          </p>
                          <p style={{ margin: "0.2em 0" }}>
                            <strong>RAZON SOCIAL:</strong> [RAZON SOCIAL]
                          </p>
                          <p style={{ margin: "0.2em 0" }}>
                            <strong>AGREGADOR:</strong> [AGREGADOR]
                          </p>
                          <p style={{ margin: "0.2em 0" }}>
                            <strong>TIPOLOGÍA:</strong> [TIPOLOGÍA]
                          </p>
                          <p style={{ margin: "0.2em 0" }}>
                            <strong>MES LIVE:</strong> [MES LIVE]
                          </p>
                          <p style={{ margin: "0.2em 0" }}>
                            <strong>TEST LIVE:</strong> [TEST LIVE]
                          </p>
                        </div>
                        <div
                          style={{
                            width: "50%",
                            boxSizing: "border-box",
                            padding: "0 0 0 0.5em",
                          }}
                        >
                          <p style={{ margin: "0.2em 0" }}>
                            <strong>COORDINADOR:</strong> [COORDINADOR]
                          </p>
                          <p style={{ margin: "0.2em 0" }}>
                            <strong>IMPLEMENTADOR 1:</strong> [IMPLEMENTADOR 1]
                          </p>
                          <p style={{ margin: "0.2em 0" }}>
                            <strong>IMPLEMENTADOR 2:</strong> [IMPLEMENTADOR 2]
                          </p>
                          <p style={{ margin: "0.2em 0" }}>
                            <strong>INCUBADORA:</strong> [INCUBADORA]
                          </p>
                          <p style={{ margin: "0.2em 0" }}>
                            <strong>BACKUP:</strong> [BACKUP]
                          </p>
                        </div>
                      </div>
                    </div>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: highlightVariables(
                          newEmail.body || "(Sin contenido)",
                        ),
                      }}
                      className="email-preview text-sm whitespace-pre-wrap break-words"
                      style={{
                        wordWrap: "break-word",
                        overflowWrap: "break-word",
                        hyphens: "auto",
                      }}
                    />
                  </div>
                </Card>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              variant="light"
              onPress={closeCreateEditModal}
            >
              Cancelar
            </Button>
            <Button
              color="primary"
              isLoading={isEditModalOpen ? updateLoading : createLoading}
              onPress={isEditModalOpen ? handleSaveEdit : handleSaveCreate}
            >
              {isEditModalOpen ? "Guardar Cambios" : "Crear Email"}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Email Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSelectedEmail(null);
        }}
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-bold">Confirmar Eliminación</h3>
          </ModalHeader>
          <ModalBody>
            <p>
              ¿Está seguro de que desea eliminar el email{" "}
              <span className="font-semibold">{selectedEmail?.name}</span>?
              <span className="block mt-2 text-gray-600 text-sm">
                Esta acción no se puede deshacer.
              </span>
            </p>
          </ModalBody>
          <ModalFooter>
            <Button
              color="default"
              variant="flat"
              onPress={() => {
                setIsDeleteModalOpen(false);
                setSelectedEmail(null);
              }}
            >
              Cancelar
            </Button>
            <Button
              color="danger"
              isLoading={deleteLoading}
              onPress={handleConfirmDelete}
            >
              Eliminar Email
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}
