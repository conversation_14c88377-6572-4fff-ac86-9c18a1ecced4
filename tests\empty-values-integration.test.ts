// Integration test to verify that empty values are properly handled
// This simulates the complete flow from field change to API call
// Run with: bun run tests/empty-values-integration.test.ts

import { transformComponentFieldsToApi } from '../utils/project-transformers';

// Mock original field data (simulating what comes from API)
const mockOriginalFields = [
  {
    id: 1,
    name: "Test Selection Field",
    type: "selection" as const,
    value: "option1", // Originally has a value
    observations: "Original observation",
    status: "PENDING" as const,
    is_active: true,
    is_milestone: false,
    weight: 10,
    subphase: "Test Subphase",
    selection_options: [
      { text: "option1", countsAsCompleted: false },
      { text: "option2", countsAsCompleted: true }
    ],
    subtask: null
  },
  {
    id: 2,
    name: "Test Input Field",
    type: "informative" as const,
    value: "Some text", // Originally has text
    observations: "Another observation",
    status: "PENDING" as const,
    is_active: true,
    is_milestone: false,
    weight: 20,
    subphase: "Test Subphase",
    selection_options: null,
    subtask: null
  }
];

function testEmptyValueHandling() {
  console.log("=== Testing Empty Value Handling ===\n");

  // Test 1: User clears a selection field
  console.log("Test 1: User clears a selection field");
  const updatedFields1 = { 1: "" }; // User deselected the option
  const updatedObservations1 = {};
  
  const result1 = transformComponentFieldsToApi(updatedFields1, updatedObservations1, mockOriginalFields);
  
  console.log("Original value:", mockOriginalFields[0].value);
  console.log("User cleared to:", updatedFields1[1]);
  console.log("API will receive:", result1.fields[0].value);
  console.log("Expected: empty string should be sent to API");
  
  const test1Pass = result1.fields[0].value === "" && result1.fields[0].field_id === 1;
  console.log("✅ Test 1 PASS:", test1Pass);
  console.log("");

  // Test 2: User clears an input field
  console.log("Test 2: User clears an input field");
  const updatedFields2 = { 2: "" }; // User deleted all text
  const updatedObservations2 = {};
  
  const result2 = transformComponentFieldsToApi(updatedFields2, updatedObservations2, mockOriginalFields);
  
  console.log("Original value:", mockOriginalFields[1].value);
  console.log("User cleared to:", updatedFields2[2]);
  console.log("API will receive:", result2.fields[0].value);
  console.log("Expected: empty string should be sent to API");
  
  const test2Pass = result2.fields[0].value === "" && result2.fields[0].field_id === 2;
  console.log("✅ Test 2 PASS:", test2Pass);
  console.log("");

  // Test 3: User clears an observation
  console.log("Test 3: User clears an observation");
  const updatedFields3 = {};
  const updatedObservations3 = { 1: "" }; // User deleted observation text
  
  const result3 = transformComponentFieldsToApi(updatedFields3, updatedObservations3, mockOriginalFields);
  
  console.log("Original observation:", mockOriginalFields[0].observations);
  console.log("User cleared to:", updatedObservations3[1]);
  console.log("API will receive:", result3.fields[0].observations);
  console.log("Expected: empty string should be sent to API");
  
  const test3Pass = result3.fields[0].observations === "" && result3.fields[0].field_id === 1;
  console.log("✅ Test 3 PASS:", test3Pass);
  console.log("");

  // Test 4: User sets field to empty and then changes observation
  console.log("Test 4: User clears field value AND observation");
  const updatedFields4 = { 1: "" }; // Cleared selection
  const updatedObservations4 = { 1: "" }; // Cleared observation
  
  const result4 = transformComponentFieldsToApi(updatedFields4, updatedObservations4, mockOriginalFields);
  
  console.log("Original value:", mockOriginalFields[0].value);
  console.log("Original observation:", mockOriginalFields[0].observations);
  console.log("User cleared value to:", updatedFields4[1]);
  console.log("User cleared observation to:", updatedObservations4[1]);
  console.log("API will receive value:", result4.fields[0].value);
  console.log("API will receive observation:", result4.fields[0].observations);
  console.log("Expected: both should be empty strings");
  
  const test4Pass = result4.fields[0].value === "" && 
                   result4.fields[0].observations === "" && 
                   result4.fields[0].field_id === 1;
  console.log("✅ Test 4 PASS:", test4Pass);
  console.log("");

  return test1Pass && test2Pass && test3Pass && test4Pass;
}

// Simulate the onChange callback from Field component
function simulateFieldChange(fieldId: number, newValue: string, newObservation?: string) {
  console.log("=== Simulating Field Component onChange ===");
  console.log(`Field ${fieldId} changed to: "${newValue}"`);
  if (newObservation !== undefined) {
    console.log(`Observation changed to: "${newObservation}"`);
  }
  
  // This simulates what happens in the project page when a field changes
  const updatedFields: Record<number, string> = {};
  const updatedObservations: Record<number, string> = {};
  
  updatedFields[fieldId] = newValue;
  if (newObservation !== undefined) {
    updatedObservations[fieldId] = newObservation;
  }
  
  // Transform for API
  const apiPayload = transformComponentFieldsToApi(updatedFields, updatedObservations, mockOriginalFields);
  
  console.log("API payload that would be sent:", JSON.stringify(apiPayload, null, 2));
  console.log("");
  
  return apiPayload;
}

// Run tests
console.log("🧪 Testing empty value handling in field updates\n");

const allTestsPass = testEmptyValueHandling();

console.log("=== Field Change Simulation ===");
simulateFieldChange(1, ""); // User clears a selection
simulateFieldChange(2, "", ""); // User clears input and observation

console.log("=== SUMMARY ===");
console.log("All tests passed:", allTestsPass);

if (allTestsPass) {
  console.log("✅ Empty values are now properly handled!");
  console.log("✅ When users clear fields or deselect options, the empty values will be uploaded to the API.");
} else {
  console.log("❌ There are still issues with empty value handling.");
}
