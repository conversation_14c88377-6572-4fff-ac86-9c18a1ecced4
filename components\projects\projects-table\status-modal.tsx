import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  TableCell,
  Select,
  SelectItem,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  <PERSON><PERSON>,
  Card,
  Spinner,
} from "@heroui/react";
import {
  Autocomplete,
  AutocompleteItem,
  AutocompleteSection,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useQuery, useMutation } from "@apollo/client";

import { ProjectInfo } from "./project-info";
import { HistoryModal } from "./history-modal";
import { EditModal } from "./edit-modal";

import { useProjectUsers } from "@/hooks/users/useProjectUsers";
import { useProjects } from "@/hooks/projects/useProjects";
import { useProductionTeams } from "@/hooks/teams/useProductionTeams";
import { useTemplates } from "@/hooks/templates/useTemplates";
import { getStatusStyle } from "@/components/primitives";
import { Project } from "@/types/projects";
import { ContactsModal } from "@/components/contacts/contacts-modal";
import {
  GET_PROJECT_ALL_DATES,
  UPDATE_PROJECT_USERS,
} from "@/graphql/operations/projects";
import { useAuth } from "@/hooks/auth/useAuth";

interface StatusModalProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onClose: () => void;
  selectedProject: Project | null;
}

export const StatusModal = ({
  isOpen,
  setIsOpen,
  onClose,
  selectedProject,
}: StatusModalProps) => {
  const { loading, error, data, refetch } = useQuery(GET_PROJECT_ALL_DATES, {
    variables: {
      id: selectedProject?.id ? parseInt(selectedProject.id) : null,
    },
    skip: !selectedProject?.id,
  });

  const { hasPermission } = useAuth();
  const [canViewContacts, setCanViewContacts] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    setCanViewContacts(hasPermission("visualizar_contactos"));
  }, [hasPermission]);

  const [projectId, setProjectId] = useState<number | null>(null);

  const [updateProjectUsers] = useMutation(UPDATE_PROJECT_USERS);

  // Get implementations from useProjects hook
  const { implementations, fetchImplementationTypes } = useProjects();
  const { projectUsers } = useProjectUsers();

  // Get production teams and templates
  const { productionTeams, fetchProductionTeams } = useProductionTeams();
  const { templates, fetchActiveTemplates } = useTemplates();

  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [selectedHistory, setSelectedHistory] = useState<number>(-1);
  const [selectedHistoryProject, setSelectedHistoryProject] = useState<
    number | null
  >(null);
  const [status, setStatus] = React.useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isContactsModalOpen, setIsContactsModalOpen] = useState(false);

  // New state variables for Autocomplete selections (storing user IDs)
  const [coordinatorId, setCoordinatorId] = useState<number | null>(null);
  const [backupId, setBackupId] = useState<number | null>(null);
  const [implementer1Id, setImplementer1Id] = useState<number | null>(null);
  const [implementer2Id, setImplementer2Id] = useState<number | null>(null);
  const [teamId, setTeamId] = useState<number | null>(null);
  const [incubatorId, setIncubatorId] = useState<number | null>(null);
  const [templateId, setTemplateId] = useState<number | null>(null);

  // State variables for selected display names (for selectedKey)
  const [selectedCoordinator, setSelectedCoordinator] = useState<string | null>(
    null,
  );
  const [selectedBackup, setSelectedBackup] = useState<string | null>(null);
  const [selectedImplementer1, setSelectedImplementer1] = useState<
    string | null
  >(null);
  const [selectedImplementer2, setSelectedImplementer2] = useState<
    string | null
  >(null);
  const [selectedTeam, setSelectedTeam] = useState<string | null>(null);
  const [selectedIncubator, setSelectedIncubator] = useState<string | null>(
    null,
  );
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  const [canEditTemplate, setCanEditTemplate] = useState(false);

  // Function to clear all form data
  const clearFormData = () => {
    setCoordinatorId(null);
    setBackupId(null);
    setImplementer1Id(null);
    setImplementer2Id(null);
    setTeamId(null);
    setIncubatorId(null);
    setTemplateId(null);
    setStatus(null);

    // Clear display names
    setSelectedCoordinator(null);
    setSelectedBackup(null);
    setSelectedImplementer1(null);
    setSelectedImplementer2(null);
    setSelectedTeam(null);
    setSelectedIncubator(null);
    setSelectedTemplate(null);
  };

  useEffect(() => {
    if (!data || !data.project) return;

    let totalPercentage = 10;

    if (data.project.percentages && data.project.percentages.length > 0) {
      // Parse the JSON strings into objects first
      const parsedPercentages = data.project.percentages.map((item: string) => {
        try {
          return JSON.parse(item);
        } catch (error) {
          console.error("Error parsing percentage:", error);

          // Handle parsing error silently
          return { percentage: 0 };
        }
      });

      // Calculate the total percentage
      totalPercentage = parsedPercentages.reduce(
        (sum: number, item: { percentage: number }) =>
          sum + (item.percentage || 0),
        0,
      );

      // Calculate the average percentage
      totalPercentage = Math.round(totalPercentage / parsedPercentages.length);
    }

    setCanEditTemplate(totalPercentage == 0);
  }, [data]);

  useEffect(() => {
    if (selectedProject) {
      setStatus(selectedProject.estado);

      if (selectedProject?.id) {
        setProjectId(parseInt(selectedProject.id));
      }
    }
  }, [selectedProject]);

  // Initialize selected display names when project data loads
  useEffect(() => {
    if (data?.project) {
      setSelectedCoordinator(data.project.coordinator?.name || null);
      setSelectedBackup(data.project.backup?.name || null);
      setSelectedImplementer1(data.project.implementer1?.name || null);
      setSelectedImplementer2(data.project.implementer2?.name || null);
      setSelectedTeam(data.project.team?.name || null);
      setSelectedIncubator(data.project.incubator?.name || null);
      setSelectedTemplate(data.project.template?.name || null);
    }
  }, [data?.project]);

  // Clear form data when modal is closed
  useEffect(() => {
    if (!isOpen) {
      clearFormData();
    }
  }, [isOpen]);

  // Fetch data when modal is opened - only once per modal open
  useEffect(() => {
    if (isOpen && selectedProject?.id) {
      fetchImplementationTypes();
      fetchProductionTeams();
      fetchActiveTemplates();

      refetch({
        id: parseInt(selectedProject.id),
      });
    }
  }, [isOpen, selectedProject?.id]);

  // Fetch data when modal opens - removed complex loading state to avoid infinite loops

  const handleSubmit = async () => {
    if (!selectedProject?.id) return;

    try {
      setIsSubmitting(true);
      // Create the input object for the mutation
      const input: any = {};

      // Only include fields that have been changed
      if (coordinatorId !== null) input.coordinatorId = coordinatorId;
      if (backupId !== null) input.backupId = backupId;
      if (implementer1Id !== null) input.implementer1Id = implementer1Id;
      if (implementer2Id !== null) input.implementer2Id = implementer2Id;
      if (teamId !== null) input.teamId = teamId;
      if (incubatorId !== null) input.incubatorId = incubatorId;
      if (templateId !== null) input.templateId = templateId;
      if (status !== null)
        input.status =
          status === "En curso"
            ? "IN_PROGRESS"
            : status === "Prevista"
              ? "SCHEDULED"
              : status === "On hold"
                ? "ON_HOLD"
                : status === "Completado"
                  ? "COMPLETED"
                  : "CANCELLED";

      // Execute the mutation
      const result = await updateProjectUsers({
        variables: {
          id: parseInt(selectedProject.id),
          input,
        },
      });

      if (result.data?.updateProject?.project) {
        console.log(
          "Project users updated successfully:",
          result.data.updateProject.project,
        );
        // Refetch the project data to update the UI
        refetch();
        onClose();
      }
    } catch (error) {
      console.error("Error updating project users:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Create phases structure from project data
  const [projectData, setProjectData] = useState(data?.project || {});

  useEffect(() => {
    if (data?.project) setProjectData(data.project);
  }, [data?.project]);

  useEffect(() => {
    refetch({ id: projectId });
  }, [isEditModalOpen]);

  const phases = [
    {
      phase: "Start",
      startDate: projectData.startRealInitialDate,
      endDate: projectData.startRealFinalDate,
      estimatedStart: projectData.startInitialDate,
      estimatedEnd: projectData.startFinalDate,
    },
    {
      phase: "Incubadora",
      startDate: projectData.incubadoraRealInitialDate,
      endDate: projectData.incubadoraRealFinalDate,
      estimatedStart: projectData.incubadoraInitialDate,
      estimatedEnd: projectData.incubadoraFinalDate,
    },
    {
      phase: "Migration",
      startDate: projectData.migrationRealInitialDate,
      endDate: projectData.migrationRealFinalDate,
      estimatedStart: projectData.migrationInitialDate,
      estimatedEnd: projectData.migrationFinalDate,
    },
    {
      phase: "Test",
      startDate: projectData.testRealInitialDate,
      endDate: projectData.testRealFinalDate,
      estimatedStart: projectData.testInitialDate,
      estimatedEnd: projectData.testFinalDate,
    },
    {
      phase: "Go Live",
      startDate: projectData.goliveRealInitialDate,
      endDate: projectData.goliveRealFinalDate,
      estimatedStart: projectData.goliveInitialDate,
      estimatedEnd: projectData.goliveFinalDate,
    },
    {
      phase: "Collection",
      startDate: projectData.collectionRealInitialDate,
      endDate: projectData.collectionRealFinalDate,
      estimatedStart: projectData.collectionInitialDate,
      estimatedEnd: projectData.collectionFinalDate,
    },
  ];

  // Loading state component with spinner
  const LoadingComponent = () => (
    <Modal backdrop="opaque" isOpen={isOpen} size="md" onClose={onClose}>
      <ModalContent>
        <Card className="border-none mx-auto p-6 shadow-none w-full">
          <div className="flex flex-col gap-4 items-center justify-center py-8">
            <Spinner color="primary" size="lg" />
            <div className="text-center">
              <h3 className="font-semibold mb-2 text-lg">Cargando</h3>
              <p className="text-center text-default-600">
                Obteniendo información del proyecto...
              </p>
            </div>
          </div>
        </Card>
      </ModalContent>
    </Modal>
  );

  // Error state component with retry button
  const ErrorComponent = () => (
    <Modal backdrop="opaque" isOpen={isOpen} size="md" onClose={onClose}>
      <ModalContent>
        <Card className="border-none mx-auto p-6 shadow-none w-full">
          <div className="flex flex-col gap-4 items-center justify-center py-8">
            <div className="bg-danger-100 dark:bg-danger-900/20 p-3 rounded-full">
              <Icon
                className="text-danger-500"
                icon="lucide:alert-circle"
                width={40}
              />
            </div>
            <div className="text-center">
              <h3 className="font-semibold mb-2 text-lg">Error</h3>
              <p className="mb-4 text-center text-default-600">
                No se pudo cargar la información del proyecto. Por favor intenta
                de nuevo.
              </p>
              <Button
                className="mt-2"
                color="primary"
                startContent={<Icon icon="lucide:refresh-cw" />}
                onPress={() => refetch()}
              >
                Reintentar
              </Button>
            </div>
          </div>
        </Card>
      </ModalContent>
    </Modal>
  );

  // Show loading only if we're actually loading data or if modal just opened and we don't have basic data
  if (loading || (isOpen && (!data?.project || projectUsers.length === 0))) {
    return <LoadingComponent />;
  }

  if (error) {
    return <ErrorComponent />;
  }

  return (
    <>
      <Modal
        key={selectedProject?.id || "no-project"}
        backdrop="opaque"
        classNames={{
          base: "bg-content1",
          header: "border-b border-divider",
          body: "py-6 overflow-visible",
          footer: "border-t border-divider",
          wrapper: " overflow-hidden",
        }}
        isDismissable={false}
        isOpen={isOpen}
        scrollBehavior="inside"
        size="5xl"
        onClose={onClose}
      >
        <ModalContent>
          {() => (
            <>
              <ModalHeader className="w-full flex items-center justify-center">
                <div
                  className="flex justify-between items-center"
                  style={{ width: "95%" }}
                >
                  <ProjectInfo
                    project={data?.project || selectedProject}
                    selectedProject={selectedProject}
                  />
                </div>
              </ModalHeader>
              <ModalBody className="overflow-y-auto">
                <>
                  <>
                    <div className="w-full">
                      {/* First row - 3 autocomplete components */}
                      <div className="grid grid-cols-4 gap-4 mb-4">
                        <Autocomplete
                          className="w-full"
                          label="Coordinador"
                          labelPlacement="outside"
                          placeholder="Seleccionar coordinador"
                          radius="sm"
                          selectedKey={selectedCoordinator}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null || key === "") {
                              setCoordinatorId(null);
                              setSelectedCoordinator(null);
                            } else {
                              const selectedUser = projectUsers.find(
                                (user) => user.name === key,
                              );

                              setCoordinatorId(selectedUser?.user_id || null);
                              setSelectedCoordinator(key as string);
                            }
                          }}
                        >
                          {Object.entries(
                            projectUsers.reduce(
                              (groups, user) => {
                                const groupName =
                                  user.group_name || "Sin grupo";

                                if (!groups[groupName]) {
                                  groups[groupName] = [];
                                }
                                groups[groupName].push(user);

                                return groups;
                              },
                              {} as Record<string, typeof projectUsers>,
                            ),
                          ).map(([groupName, users]) => (
                            <AutocompleteSection
                              key={groupName}
                              showDivider
                              title={groupName}
                            >
                              {users.map((user) => (
                                <AutocompleteItem key={user.name}>
                                  {user.name}
                                </AutocompleteItem>
                              ))}
                            </AutocompleteSection>
                          ))}
                        </Autocomplete>

                        <Autocomplete
                          className="w-full"
                          label="Implementador 1"
                          labelPlacement="outside"
                          placeholder="Seleccionar implementador"
                          radius="sm"
                          selectedKey={selectedImplementer1}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null || key === "") {
                              setImplementer1Id(null);
                              setSelectedImplementer1(null);
                            } else {
                              const selectedUser = projectUsers.find(
                                (user) => user.name === key,
                              );

                              setImplementer1Id(selectedUser?.user_id || null);
                              setSelectedImplementer1(key as string);
                            }
                          }}
                        >
                          {Object.entries(
                            projectUsers.reduce(
                              (groups, user) => {
                                const groupName =
                                  user.group_name || "Sin grupo";

                                if (!groups[groupName]) {
                                  groups[groupName] = [];
                                }
                                groups[groupName].push(user);

                                return groups;
                              },
                              {} as Record<string, typeof projectUsers>,
                            ),
                          ).map(([groupName, users]) => (
                            <AutocompleteSection
                              key={groupName}
                              showDivider
                              title={groupName}
                            >
                              {users.map((user) => (
                                <AutocompleteItem key={user.name}>
                                  {user.name}
                                </AutocompleteItem>
                              ))}
                            </AutocompleteSection>
                          ))}
                        </Autocomplete>
                        <Autocomplete
                          className="w-full"
                          label="Implementador 2"
                          labelPlacement="outside"
                          placeholder="Seleccionar implementador"
                          radius="sm"
                          selectedKey={selectedImplementer2}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null || key === "") {
                              setImplementer2Id(null);
                              setSelectedImplementer2(null);
                            } else {
                              const selectedUser = projectUsers.find(
                                (user) => user.name === key,
                              );

                              setImplementer2Id(selectedUser?.user_id || null);
                              setSelectedImplementer2(key as string);
                            }
                          }}
                        >
                          {Object.entries(
                            projectUsers.reduce(
                              (groups, user) => {
                                const groupName =
                                  user.group_name || "Sin grupo";

                                if (!groups[groupName]) {
                                  groups[groupName] = [];
                                }
                                groups[groupName].push(user);

                                return groups;
                              },
                              {} as Record<string, typeof projectUsers>,
                            ),
                          ).map(([groupName, users]) => (
                            <AutocompleteSection
                              key={groupName}
                              showDivider
                              title={groupName}
                            >
                              {users.map((user) => (
                                <AutocompleteItem key={user.name}>
                                  {user.name}
                                </AutocompleteItem>
                              ))}
                            </AutocompleteSection>
                          ))}
                        </Autocomplete>

                        <Autocomplete
                          className="w-full"
                          isDisabled={!canEditTemplate}
                          label="Plantilla"
                          labelPlacement="outside"
                          placeholder="Seleccionar plantilla"
                          radius="sm"
                          selectedKey={selectedTemplate}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null || key === "") {
                              setTemplateId(null);
                              setSelectedTemplate(null);
                            } else {
                              const selectedTemplateObj = templates.find(
                                (template) => template.name === key,
                              );

                              setTemplateId(selectedTemplateObj?.id || null);
                              setSelectedTemplate(key as string);
                            }
                          }}
                        >
                          {templates.map((template) => (
                            <AutocompleteItem key={template.name}>
                              {template.name}
                            </AutocompleteItem>
                          ))}
                        </Autocomplete>
                      </div>

                      {/* Second row - 5 autocomplete/select components */}
                      <div className="grid grid-cols-4 gap-4">
                        <Autocomplete
                          className="w-full"
                          label="Incubador"
                          labelPlacement="outside"
                          placeholder="Seleccionar Incubador"
                          radius="sm"
                          selectedKey={selectedIncubator}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null || key === "") {
                              setIncubatorId(null);
                              setSelectedIncubator(null);
                            } else {
                              const selectedUser = projectUsers.find(
                                (user) => user.name === key,
                              );

                              setIncubatorId(selectedUser?.user_id || null);
                              setSelectedIncubator(key as string);
                            }
                          }}
                        >
                          {Object.entries(
                            projectUsers.reduce(
                              (groups, user) => {
                                const groupName =
                                  user.group_name || "Sin grupo";

                                if (!groups[groupName]) {
                                  groups[groupName] = [];
                                }
                                groups[groupName].push(user);

                                return groups;
                              },
                              {} as Record<string, typeof projectUsers>,
                            ),
                          ).map(([groupName, users]) => (
                            <AutocompleteSection
                              key={groupName}
                              showDivider
                              title={groupName}
                            >
                              {users.map((user) => (
                                <AutocompleteItem key={user.name}>
                                  {user.name}
                                </AutocompleteItem>
                              ))}
                            </AutocompleteSection>
                          ))}
                        </Autocomplete>

                        <Autocomplete
                          className="w-full"
                          label="Backup"
                          labelPlacement="outside"
                          placeholder="Seleccionar Backup"
                          radius="sm"
                          selectedKey={selectedBackup}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null || key === "") {
                              setBackupId(null);
                              setSelectedBackup(null);
                            } else {
                              const selectedUser = projectUsers.find(
                                (user) => user.name === key,
                              );

                              setBackupId(selectedUser?.user_id || null);
                              setSelectedBackup(key as string);
                            }
                          }}
                        >
                          {Object.entries(
                            projectUsers.reduce(
                              (groups, user) => {
                                const groupName =
                                  user.group_name || "Sin grupo";

                                if (!groups[groupName]) {
                                  groups[groupName] = [];
                                }
                                groups[groupName].push(user);

                                return groups;
                              },
                              {} as Record<string, typeof projectUsers>,
                            ),
                          ).map(([groupName, users]) => (
                            <AutocompleteSection
                              key={groupName}
                              showDivider
                              title={groupName}
                            >
                              {users.map((user) => (
                                <AutocompleteItem key={user.name}>
                                  {user.name}
                                </AutocompleteItem>
                              ))}
                            </AutocompleteSection>
                          ))}
                        </Autocomplete>

                        <Autocomplete
                          className="w-full"
                          label="Equipo P."
                          labelPlacement="outside"
                          placeholder="Seleccionar equipo"
                          radius="sm"
                          selectedKey={selectedTeam}
                          variant="bordered"
                          onSelectionChange={(key) => {
                            if (key === null || key === "") {
                              setTeamId(null);
                              setSelectedTeam(null);
                            } else {
                              const selectedTeamObj = productionTeams.find(
                                (team) => team.name === key,
                              );

                              setTeamId(selectedTeamObj?.id || null);
                              setSelectedTeam(key as string);
                            }
                          }}
                        >
                          {productionTeams.map((team) => (
                            <AutocompleteItem key={team.name}>
                              {team.name}
                            </AutocompleteItem>
                          ))}
                        </Autocomplete>

                        <Select
                          key={selectedProject?.estado}
                          classNames={{
                            value: "font-medium",
                          }}
                          label="Estado"
                          labelPlacement="outside"
                          placeholder="Seleccionar estado"
                          radius="sm"
                          selectedKeys={status ? [status] : undefined}
                          startContent={
                            status ? (
                              status === "En curso" ? (
                                <Icon
                                  className="text-success-500"
                                  icon="lucide:play-circle"
                                  width={16}
                                />
                              ) : status === "Prevista" ? (
                                <Icon
                                  className="text-orange-500"
                                  icon="lucide:clock"
                                  width={16}
                                />
                              ) : status === "On hold" ? (
                                <Icon
                                  className="text-black"
                                  icon="lucide:pause-circle"
                                  width={16}
                                />
                              ) : status === "Completado" ? (
                                <Icon
                                  className="text-primary"
                                  icon="lucide:check-circle"
                                  width={16}
                                />
                              ) : (
                                <Icon
                                  className="text-danger-500"
                                  icon="lucide:x-circle"
                                  width={16}
                                />
                              )
                            ) : (
                              <Icon
                                className="text-default-500"
                                icon="lucide:circle"
                                width={16}
                              />
                            )
                          }
                          onChange={(e) => setStatus(e.target.value)}
                        >
                          {[
                            "En curso",
                            "Prevista",
                            "On hold",
                            "Completado",
                            "Cancelado",
                          ].map((state) => (
                            <SelectItem key={state}>{state}</SelectItem>
                          ))}
                        </Select>
                      </div>
                    </div>
                    <div className="overflow-x-auto">
                      <Card className="shadow-none border border-divider">
                        <Table
                          removeWrapper
                          aria-label="Project dates"
                          classNames={{
                            th: "bg-default-50 font-medium text-default-700 text-sm sticky top-0 z-10",
                            td: "py-3 text-sm",
                            table: "min-w-full",
                          }}
                        >
                          <TableHeader>
                            <TableColumn width={120}>Fases</TableColumn>
                            <TableColumn>Fecha inicio prevista</TableColumn>
                            <TableColumn>Fecha inicio real</TableColumn>
                            <TableColumn>Fecha fin prevista</TableColumn>
                            <TableColumn>Fecha fin real</TableColumn>
                            <TableColumn>Mes test 1</TableColumn>
                            <TableColumn>Mes test 2</TableColumn>
                            <TableColumn className="text-center" width={90}>
                              Acciones
                            </TableColumn>
                          </TableHeader>
                          <TableBody>
                            {phases
                              .sort((a, b) => {
                                const phaseOrder = [
                                  "Start",
                                  "Collection",
                                  "Migration",
                                  "Test",
                                  "Go Live",
                                  "Incubadora",
                                ];

                                return (
                                  phaseOrder.indexOf(a.phase) -
                                  phaseOrder.indexOf(b.phase)
                                );
                              })
                              .map((phase, index) => (
                                <TableRow key={index}>
                                  <TableCell
                                    className="font-medium"
                                    style={{
                                      backgroundColor: getStatusStyle(
                                        phase.phase,
                                      ),
                                    }}
                                  >
                                    {phase.phase.toLowerCase() === "incubadora"
                                      ? "Take Off"
                                      : phase.phase}
                                  </TableCell>
                                  <TableCell>
                                    {phase.estimatedStart || "-"}
                                  </TableCell>
                                  <TableCell>
                                    {phase.startDate || "-"}
                                  </TableCell>
                                  <TableCell>
                                    {phase.estimatedEnd || "-"}
                                  </TableCell>
                                  <TableCell>{phase.endDate || "-"}</TableCell>
                                  <TableCell className="">
                                    {phase.phase === "Test"
                                      ? projectData.month1Test || "-"
                                      : "-"}
                                  </TableCell>
                                  <TableCell className="">
                                    {phase.phase === "Test"
                                      ? projectData.month2Test || "-"
                                      : "-"}
                                  </TableCell>
                                  <TableCell className="text-center flex justify-center items-center gap-2 min-h-[40px]">
                                    <Button
                                      isIconOnly
                                      color="primary"
                                      size="sm"
                                      variant="flat"
                                      onPress={() => {
                                        setSelectedHistory(index);
                                        setIsEditModalOpen(true);
                                      }}
                                    >
                                      <Icon icon="lucide:edit-3" width={18} />
                                    </Button>
                                    <Button
                                      isIconOnly
                                      color="secondary"
                                      size="sm"
                                      variant="flat"
                                      onPress={() => {
                                        setSelectedHistoryProject(
                                          selectedProject?.id
                                            ? parseInt(selectedProject.id)
                                            : null,
                                        );
                                        setIsHistoryModalOpen(true);
                                      }}
                                    >
                                      <Icon icon="lucide:history" width={18} />
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ))}
                          </TableBody>
                        </Table>
                      </Card>
                    </div>
                  </>
                </>
              </ModalBody>
              <ModalFooter className="w-full flex justify-between">
                <div>
                  <Button
                    isIconOnly
                    aria-label="Contacts"
                    className="text-white"
                    color="primary"
                    isDisabled={!canViewContacts}
                    onPress={() => {
                      setIsContactsModalOpen(true);
                      onClose();
                    }}
                  >
                    <Icon icon="lucide:users" width={20} />
                  </Button>
                </div>
                <div>
                  <Button
                    color="danger"
                    isDisabled={isSubmitting}
                    radius="sm"
                    variant="light"
                    onPress={onClose}
                  >
                    Cancelar
                  </Button>
                  <Button
                    color="primary"
                    isDisabled={isSubmitting}
                    radius="sm"
                    type="submit"
                    onPress={handleSubmit}
                  >
                    {isSubmitting ? <Spinner size="sm" /> : "Guardar"}
                  </Button>
                </div>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      <HistoryModal
        isOpen={isHistoryModalOpen}
        selectedProject={selectedHistoryProject}
        onClose={() => {
          setIsHistoryModalOpen(false);
          // setIsOpen(true);
        }}
      />

      <EditModal
        isOpen={isEditModalOpen}
        phase={selectedHistory >= 0 ? phases[selectedHistory].phase : ""}
        selectedHistory={selectedHistory}
        selectedProject={selectedProject}
        onClose={() => {
          setIsEditModalOpen(false);
          setIsOpen(true);
        }}
      />

      <ContactsModal
        isOpen={isContactsModalOpen}
        selectedProject={projectId}
        onClose={() => {
          setIsContactsModalOpen(false);
          setIsOpen(true);
        }}
      />
    </>
  );
};
