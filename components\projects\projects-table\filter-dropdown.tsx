"use client";
import { FaCaretDown } from "react-icons/fa";
import { CheckboxGroup, Checkbox } from "@heroui/react";
import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  DropdownSection,
  Button,
} from "@nextui-org/react";

interface FilterDropdownProps {
  column: string;
  title: string;
  activeFilters: Record<string, string[]>;
  sortConfig: { column: string; direction: "asc" | "desc" } | null;
  onSort: (column: string, direction: "asc" | "desc") => void;
  onFilter: (column: string, values: string[]) => void;
  items: string[];
  number?: boolean;
  displayText?: Record<string, string>;
}

export const FilterDropdown = ({
  column,
  title,
  activeFilters,
  sortConfig,
  onSort,
  onFilter,
  items,
  number = false,
  displayText,
}: FilterDropdownProps) => {
  // Function to select all checkboxes
  const handleSelectAll = () => {
    onFilter(column, items);
  };

  // Function to deselect all checkboxes
  const handleDeselectAll = () => {
    onFilter(column, []);
  };

  return (
    <Dropdown
      classNames={{
        base: "before:bg-default-200",
        content:
          "py-1 px-1 border border-default-200 bg-gradient-to-br from-white to-default-200 dark:from-default-50 dark:to-black",
      }}
    >
      <DropdownTrigger>
        <span
          className={`flex ${activeFilters[column] ? "text-primary font-bold" : ""}`}
        >
          {title} &nbsp;
          <FaCaretDown className="mt-1" />
        </span>
      </DropdownTrigger>
      <DropdownMenu aria-label={`${title} filter`} closeOnSelect={false}>
        <DropdownSection showDivider title="Ordenar">
          <DropdownItem
            key="ascending_sort"
            startContent={
              sortConfig?.column === column &&
              sortConfig?.direction === "asc" &&
              "✓"
            }
            onPress={() => onSort(column, "asc")}
          >
            Sort A to Z
          </DropdownItem>
          <DropdownItem
            key="descending_sort"
            startContent={
              sortConfig?.column === column &&
              sortConfig?.direction === "desc" &&
              "✓"
            }
            onPress={() => onSort(column, "desc")}
          >
            Sort Z to A
          </DropdownItem>
        </DropdownSection>
        <DropdownSection showDivider title="Filtros">
          <DropdownItem key="filter-actions">
            <div className="flex gap-2 mb-2">
              <Button
                className="max-w-8 m-0"
                variant="flat"
                onPress={handleSelectAll}
              >
                Todos
              </Button>
              <Button
                className="max-w-8"
                variant="flat"
                onPress={handleDeselectAll}
              >
                Ninguno
              </Button>
            </div>
          </DropdownItem>
          <DropdownItem key="filter">
            <div className="max-h-[200px] overflow-y-auto custom-scrollbar">
              <CheckboxGroup
                orientation="vertical"
                value={activeFilters[column] || []}
                onChange={(values) => onFilter(column, values as string[])}
              >
                {items
                  .sort((a, b) => {
                    if (number) {
                      return parseFloat(a) - parseFloat(b);
                    }

                    // If the items are the phase items, sort them in the following way, start - collection - migration - test - take off - go live
                    const phaseOrder = [
                      "START",
                      "COLLECTION",
                      "MIGRATION",
                      "TEST",
                      "GO LIVE",
                      "TAKE OFF",
                    ];
                    const aIndex = phaseOrder.indexOf(a);
                    const bIndex = phaseOrder.indexOf(b);
                    if (aIndex !== -1 && bIndex !== -1) {
                      return aIndex - bIndex;
                    }

                    return a.localeCompare(b);
                  })
                  .map((item) => (
                    <Checkbox key={item} value={item}>
                      {displayText && displayText[item]
                        ? (displayText[item] === "INCUBADORA" ? "TAKE OFF" : displayText[item])
                        : item === "INCUBADORA" ? "TAKE OFF" : item}
                    </Checkbox>
                  ))}
              </CheckboxGroup>
            </div>
          </DropdownItem>
        </DropdownSection>
      </DropdownMenu>
    </Dropdown>
  );
};
