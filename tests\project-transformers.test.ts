// Simple test for transformComponentFieldsToApi function
// This can be run with: bun run tests/project-transformers.test.ts

import { transformComponentFieldsToApi } from '../utils/project-transformers';

// Mock data
const originalFields = [
  {
    id: 1,
    name: "Test Field 1",
    type: "informative",
    value: "original value 1",
    observations: "original observation 1",
    status: "PENDING",
    is_active: true,
    is_milestone: false,
    weight: 10,
    subphase: "Test Subphase",
    selection_options: null,
    subtask: null
  },
  {
    id: 2,
    name: "Test Field 2", 
    type: "task",
    value: "original value 2",
    observations: "original observation 2",
    status: "PENDING",
    is_active: true,
    is_milestone: false,
    weight: 20,
    subphase: "Test Subphase",
    selection_options: null,
    subtask: null
  }
];

// Test cases
function testObservationUpdates() {
  console.log("Testing observation updates...");
  
  // Test 1: Only value updated
  const updatedFields1 = { 1: "new value 1" };
  const updatedObservations1 = {};
  
  const result1 = transformComponentFieldsToApi(updatedFields1, updatedObservations1, originalFields);
  
  console.log("Test 1 - Only value updated:");
  console.log("Expected: field_id=1, value='new value 1', observations='original observation 1'");
  console.log("Actual:", result1.fields[0]);
  
  const test1Pass = result1.fields[0].field_id === 1 && 
                   result1.fields[0].value === "new value 1" &&
                   result1.fields[0].observations === "original observation 1";
  console.log("Test 1 PASS:", test1Pass);
  console.log("");
  
  // Test 2: Only observation updated
  const updatedFields2 = {};
  const updatedObservations2 = { 2: "new observation 2" };
  
  const result2 = transformComponentFieldsToApi(updatedFields2, updatedObservations2, originalFields);
  
  console.log("Test 2 - Only observation updated:");
  console.log("Expected: field_id=2, value='original value 2', observations='new observation 2'");
  console.log("Actual:", result2.fields[0]);
  
  const test2Pass = result2.fields[0].field_id === 2 && 
                   result2.fields[0].value === "original value 2" &&
                   result2.fields[0].observations === "new observation 2";
  console.log("Test 2 PASS:", test2Pass);
  console.log("");
  
  // Test 3: Both value and observation updated
  const updatedFields3 = { 1: "new value 1" };
  const updatedObservations3 = { 1: "new observation 1" };
  
  const result3 = transformComponentFieldsToApi(updatedFields3, updatedObservations3, originalFields);
  
  console.log("Test 3 - Both value and observation updated:");
  console.log("Expected: field_id=1, value='new value 1', observations='new observation 1'");
  console.log("Actual:", result3.fields[0]);
  
  const test3Pass = result3.fields[0].field_id === 1 && 
                   result3.fields[0].value === "new value 1" &&
                   result3.fields[0].observations === "new observation 1";
  console.log("Test 3 PASS:", test3Pass);
  console.log("");

  // Test 4: Empty value should be uploaded (not ignored)
  const updatedFields4 = { 1: "" }; // Empty string
  const updatedObservations4 = {};

  const result4 = transformComponentFieldsToApi(updatedFields4, updatedObservations4, originalFields);

  console.log("Test 4 - Empty value should be uploaded:");
  console.log("Expected: field_id=1, value='', observations='original observation 1'");
  console.log("Actual:", result4.fields[0]);

  const test4Pass = result4.fields[0].field_id === 1 &&
                   result4.fields[0].value === "" &&
                   result4.fields[0].observations === "original observation 1";
  console.log("Test 4 PASS:", test4Pass);
  console.log("");

  // Test 5: Empty observation should be uploaded (not ignored)
  const updatedFields5 = {};
  const updatedObservations5 = { 2: "" }; // Empty string

  const result5 = transformComponentFieldsToApi(updatedFields5, updatedObservations5, originalFields);

  console.log("Test 5 - Empty observation should be uploaded:");
  console.log("Expected: field_id=2, value='original value 2', observations=''");
  console.log("Actual:", result5.fields[0]);

  const test5Pass = result5.fields[0].field_id === 2 &&
                   result5.fields[0].value === "original value 2" &&
                   result5.fields[0].observations === "";
  console.log("Test 5 PASS:", test5Pass);
  console.log("");

  // Test 6: Multiple fields updated
  const updatedFields6 = { 1: "new value 1", 2: "new value 2" };
  const updatedObservations6 = { 1: "new observation 1" };

  const result6 = transformComponentFieldsToApi(updatedFields6, updatedObservations6, originalFields);

  console.log("Test 6 - Multiple fields updated:");
  console.log("Expected: 2 fields in result");
  console.log("Actual fields count:", result6.fields.length);

  const field1 = result6.fields.find(f => f.field_id === 1);
  const field2 = result6.fields.find(f => f.field_id === 2);

  const test6Pass = result6.fields.length === 2 &&
                   field1.value === "new value 1" &&
                   field1.observations === "new observation 1" &&
                   field2.value === "new value 2" &&
                   field2.observations === "original observation 2";
  console.log("Test 6 PASS:", test6Pass);

  return test1Pass && test2Pass && test3Pass && test4Pass && test5Pass && test6Pass;
}

// Run tests
console.log("=== Testing transformComponentFieldsToApi function ===");
const allTestsPass = testObservationUpdates();
console.log("\n=== SUMMARY ===");
console.log("All tests passed:", allTestsPass);

if (allTestsPass) {
  console.log("✅ The fix for observations not being included when saving changes is working correctly!");
} else {
  console.log("❌ There are still issues with the observation handling.");
}
