"use client";

import { useState, useEffect, useCallback } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";
import { ApiProjectResponse } from "@/types/project-api";

export function useProject(projectId: string | number) {
  const [projectData, setProjectData] = useState<ApiProjectResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProject = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(
        `${API_ROUTES.PROJECT_DETAIL}${projectId}/`
      );

      if (response.status === 200) {
        setProjectData(response.data);
      } else {
        setError("Failed to fetch project data");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch project data");
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    if (projectId) {
      fetchProject();
    }
  }, [projectId, fetchProject]);

  return {
    projectData,
    loading,
    error,
    refetch: fetchProject,
  };
}
