"use client";

import React, { useEffect } from "react";
import {
  Accordion,
  AccordionItem,
  Card,
  CardBody,
  CardHeader,
  Select,
  SelectItem,
  Switch,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { useAllFieldsQuery } from "@/graphql/schemas/generated";
import { getFieldTypeDisplayName } from "@/utils/utils";

interface FieldTypeFilter {
  statuses: ("completed" | "not_completed" | "in_progress")[];
  includeObservations: boolean;
}

interface ReportFiltersStepProps {
  selectedFields: string[];
  fieldFilters: Record<string, FieldTypeFilter>;
  onFiltersChange: (filters: Record<string, FieldTypeFilter>) => void;
  globalObservations: boolean;
  onGlobalObservationsChange: (value: boolean) => void;
}

// Filter options for each field type
const getFilterOptionsForType = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return [
        {
          key: "completed",
          label: "Completado",
          color: "success" as const,
          dotColor: "bg-success",
        },
        {
          key: "not_completed",
          label: "No completado",
          color: "danger" as const,
          dotColor: "bg-danger",
        },
        {
          key: "in_progress",
          label: "En progreso",
          color: "warning" as const,
          dotColor: "bg-warning",
        },
      ];
    case "selection":
      return [
        {
          key: "completed",
          label: "Completado",
          color: "success" as const,
          dotColor: "bg-success",
        },
        {
          key: "not_completed",
          label: "No completado",
          color: "danger" as const,
          dotColor: "bg-danger",
        },
        {
          key: "in_progress",
          label: "En progreso",
          color: "warning" as const,
          dotColor: "bg-warning",
        },
      ];
    case "task":
    case "document":
    case "task_with_subtasks":
      return [
        {
          key: "completed",
          label: "Completado",
          color: "success" as const,
          dotColor: "bg-success",
        },
        {
          key: "not_completed",
          label: "No completado",
          color: "danger" as const,
          dotColor: "bg-danger",
        },
        {
          key: "in_progress",
          label: "En progreso",
          color: "warning" as const,
          dotColor: "bg-warning",
        },
      ];
    default:
      return [
        {
          key: "completed",
          label: "Completado",
          color: "success" as const,
          dotColor: "bg-success",
        },
        {
          key: "not_completed",
          label: "No completado",
          color: "danger" as const,
          dotColor: "bg-danger",
        },
        {
          key: "in_progress",
          label: "En progreso",
          color: "warning" as const,
          dotColor: "bg-warning",
        },
      ];
  }
};

const getFieldTypeIcon = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return "heroicons:information-circle";
    case "selection":
      return "heroicons:list-bullet";
    case "task":
      return "heroicons:check-circle";
    case "document":
      return "heroicons:document";
    case "task_with_subtasks":
      return "heroicons:squares-plus";
    default:
      return "heroicons:question-mark-circle";
  }
};

export default function ReportFiltersStep({
  selectedFields,
  fieldFilters,
  onFiltersChange,
  globalObservations,
  onGlobalObservationsChange,
}: ReportFiltersStepProps) {
  const { data: fieldsData } = useAllFieldsQuery();

  const fields = fieldsData?.allFields || [];

  // Get selected field details
  const selectedFieldsData = fields.filter((field) =>
    selectedFields.includes(field?.id || ""),
  );

  // Get unique field types from selected fields
  const selectedFieldTypes = Array.from(
    new Set(selectedFieldsData.map((field) => field?.type).filter(Boolean)),
  );

  // Initialize filters for field types
  useEffect(() => {
    const newFilters: Record<string, FieldTypeFilter> = {};

    selectedFieldTypes.forEach((fieldType) => {
      if (fieldType && !fieldFilters[fieldType]) {
        newFilters[fieldType] = {
          statuses: ["completed"],
          includeObservations: globalObservations,
        };
      }
    });

    // Only update if there are new field types
    if (Object.keys(newFilters).length > 0) {
      onFiltersChange({ ...fieldFilters, ...newFilters });
    }
  }, [selectedFieldTypes, globalObservations]);

  // Update all field filters when global observations changes
  useEffect(() => {
    const updatedFilters: Record<string, FieldTypeFilter> = {};
    let hasChanges = false;

    Object.keys(fieldFilters).forEach((fieldType) => {
      if (fieldFilters[fieldType].includeObservations !== globalObservations) {
        updatedFilters[fieldType] = {
          ...fieldFilters[fieldType],
          includeObservations: globalObservations,
        };
        hasChanges = true;
      } else {
        updatedFilters[fieldType] = fieldFilters[fieldType];
      }
    });

    if (hasChanges) {
      onFiltersChange(updatedFilters);
    }
  }, [globalObservations]);

  const handleStatusFilterChange = (
    fieldType: string,
    selectedStatuses: ("completed" | "not_completed" | "in_progress")[],
  ) => {
    onFiltersChange({
      ...fieldFilters,
      [fieldType]: {
        ...fieldFilters[fieldType],
        statuses: selectedStatuses,
      },
    });
  };

  // Group fields by type
  const fieldsByType = selectedFieldsData.reduce(
    (acc, field) => {
      const type = field?.type || "unknown";

      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(field);

      return acc;
    },
    {} as Record<string, any[]>,
  );

  if (selectedFields.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <Icon
          className="text-warning mb-4"
          icon="heroicons:exclamation-triangle"
          width={48}
        />
        <h3 className="text-lg font-semibold mb-2">
          No hay campos seleccionados
        </h3>
        <p className="text-default-500">
          Regresa al paso anterior para seleccionar los campos que deseas
          incluir en el reporte.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold mb-2">
          Configurar filtros por tipo de campo
        </h3>
        <p className="text-default-500">
          Define cómo filtrar los datos para cada tipo de campo seleccionado
        </p>
      </div>

      {/* Global Observations Control */}
      <Card className="w-full">
        <CardBody>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Icon className="text-primary" icon="heroicons:eye" width={24} />
              <div>
                <h4 className="text-lg font-semibold">
                  Control global de observaciones
                </h4>
                <p className="text-sm text-default-500">
                  Incluir o excluir observaciones para todos los tipos de campo
                </p>
              </div>
            </div>
            <Switch
              isSelected={globalObservations}
              size="lg"
              onValueChange={onGlobalObservationsChange}
            >
              <span className="text-sm font-medium">
                {globalObservations ? "Incluir todas" : "Excluir todas"}
              </span>
            </Switch>
          </div>
        </CardBody>
      </Card>

      {Object.entries(fieldsByType).map(([fieldType, fieldsOfType]) => {
        const filterOptions = getFilterOptionsForType(fieldType);
        const currentFilter = fieldFilters[fieldType] || {
          statuses: ["completed"],
          includeObservations: false,
        };

        return (
          <Card key={fieldType} className="w-full">
            <CardHeader className="pb-2">
              <div className="flex flex-col gap-4">
                <div className="flex items-center gap-3">
                  <Icon
                    className="text-primary"
                    icon={getFieldTypeIcon(fieldType)}
                    width={24}
                  />
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold">
                      {getFieldTypeDisplayName(fieldType)}
                    </h4>
                    <p className="text-sm text-default-500">
                      {fieldsOfType.length} campo(s) de este tipo
                    </p>
                  </div>
                </div>

                <div className="flex flex-col gap-3 min-w-[340px]">
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium min-w-[120px]">
                      Estados:
                    </span>
                    <div className="flex-1">
                      <Select
                        className="w-full min-w-[300px]"
                        placeholder="Seleccionar estados"
                        renderValue={() => {
                          if (currentFilter.statuses.length === 0) {
                            return (
                              <span className="text-default-400">
                                Seleccionar estados
                              </span>
                            );
                          }

                          if (currentFilter.statuses.length === 1) {
                            const selectedOption = filterOptions.find(
                              (option) =>
                                option.key === currentFilter.statuses[0],
                            );

                            return (
                              <div className="flex items-center gap-2">
                                <div
                                  className={`w-2 h-2 rounded-full ${selectedOption?.dotColor}`}
                                />
                                <span>{selectedOption?.label}</span>
                              </div>
                            );
                          }

                          if (currentFilter.statuses.length === 3) {
                            return (
                              <div className="flex items-center gap-2">
                                <div
                                  className="w-2 h-2 rounded-full relative"
                                  style={{
                                    background:
                                      "linear-gradient(300deg, var(--success), var(--warning), var(--danger))",
                                    backgroundSize: "180% 180%",
                                    animation:
                                      "gradient-animation 4s ease infinite",
                                  }}
                                />
                                <span>Todos</span>
                              </div>
                            );
                          }

                          return (
                            <span>
                              {currentFilter.statuses.map((status, index) => {
                                const option = filterOptions.find(
                                  (opt) => opt.key === status,
                                );

                                return (
                                  <span
                                    key={status}
                                    className={`inline-flex items-center gap-1 ${
                                      index > 0 ? "ml-1" : ""
                                    }`}
                                  >
                                    {option?.label}
                                    {index < currentFilter.statuses.length - 1
                                      ? ","
                                      : ""}
                                  </span>
                                );
                              })}
                            </span>
                          );
                        }}
                        selectedKeys={currentFilter.statuses}
                        selectionMode="multiple"
                        size="sm"
                        onSelectionChange={(keys) => {
                          const selectedKeys = Array.from(keys) as (
                            | "completed"
                            | "not_completed"
                            | "in_progress"
                          )[];

                          handleStatusFilterChange(fieldType, selectedKeys);
                        }}
                      >
                        {filterOptions.map((option) => (
                          <SelectItem key={option.key}>
                            <div className="flex items-center gap-2">
                              <div
                                className={`w-2 h-2 rounded-full ${option.dotColor}`}
                              />
                              {option.label}
                            </div>
                          </SelectItem>
                        ))}
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium min-w-[120px]">
                      Observaciones:
                    </span>
                    <Switch
                      isDisabled
                      isSelected={globalObservations}
                      size="sm"
                    >
                      <span className="text-sm text-default-400">
                        {globalObservations
                          ? "Incluir (controlado globalmente)"
                          : "No incluir (controlado globalmente)"}
                      </span>
                    </Switch>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="space-y-2">
                <Accordion variant="bordered">
                  <AccordionItem
                    className="w-full text-sm text-default-600"
                    title={`Campos incluidos en este tipo:`}
                  >
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {fieldsOfType
                        .sort((a, b) =>
                          a?.name.localeCompare(b?.name, undefined, {
                            sensitivity: "base",
                          }),
                        )
                        .map((field) => (
                          <div
                            key={field?.id}
                            className="flex items-center gap-2 p-2 bg-default-50 rounded-lg"
                          >
                            <div className="flex-1">
                              <div className="font-medium text-sm">
                                {field?.name}
                              </div>
                              <div className="text-xs text-default-500">
                                {field?.subphase?.phase?.name} -{" "}
                                {field?.subphase?.name}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </AccordionItem>
                </Accordion>
              </div>
            </CardBody>
          </Card>
        );
      })}
    </div>
  );
}
