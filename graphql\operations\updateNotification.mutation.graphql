mutation ExecuteUpdateNotification(
  $id: ID!
  $name: String
  $description: String
  $descriptionEn: String
  $triggerFieldId: ID
  $triggerCondition: String
  $value: String
  $emailTemplateId: ID
  $userRecipients: [String]
  $teamRecipients: [String]
  $leaderRecipients: [String]
  $roleRecipients: [String]
) {
  updateNotification(
    id: $id
    name: $name
    description: $description
    descriptionEn: $descriptionEn
    triggerFieldId: $triggerFieldId
    triggerCondition: $triggerCondition
    value: $value
    emailTemplateId: $emailTemplateId
    userRecipients: $userRecipients
    teamRecipients: $teamRecipients
    leaderRecipients: $leaderRecipients
    roleRecipients: $roleRecipients
  ) {
    notification {
      id
      name
      description
      triggerCondition
      value
      triggerField {
        id
        name
      }
      emailTemplate {
        id
        name
      }
      leaderRecipients
      teamRecipients
      userRecipients
      roleRecipients
      allRecipients
      createdAt
    }
  }
}
