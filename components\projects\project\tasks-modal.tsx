import React, { useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Chip,
  Tooltip,
} from "@heroui/react";
import { Icon } from "@iconify/react";

export interface Task {
  id: string;
  name: string;
  description: string;
  status: "En progreso" | "Ni empezada";
  dueDate: string;
}

export interface TasksModalProps {
  isOpen: boolean;
  onClose: () => void;
  tasks: any;
  onViewTask: (taskId: string) => void;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "IN_PROGRESS":
      return "warning";
    case "NOT_STARTED":
      return "danger";
    default:
      return "default";
  }
};

export const TasksModal = ({
  isOpen,
  onClose,
  tasks,
  onViewTask,
}: TasksModalProps) => {
  useEffect(() => {
    console.log("TasksModal mounted with tasks:", tasks);
  }, [tasks]);

  return (
    <Modal
      className="max-h-[80vh] max-w-[90vw] w-[90vw]"
      isOpen={isOpen}
      scrollBehavior="inside"
      size="5xl"
      onClose={onClose}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              <h2 className="text-lg font-bold">Lista de Tareas</h2>
              <p className="text-xs text-default-500">
                Gestiona tus tareas de manera eficiente
              </p>
            </ModalHeader>
            <ModalBody className="px-0 max-h-[60vh] overflow-hidden">
              <div className="px-4 flex-1 overflow-auto">
                <Table
                  aria-label="Tabla de tareas"
                  className="w-full table-fixed"
                  classNames={{
                    wrapper: "max-h-none shadow-none",
                    th: "bg-default-100 text-default-800 text-xs py-2",
                    td: "py-2 text-sm overflow-hidden",
                  }}
                >
                  <TableHeader>
                    <TableColumn className="max-w-[8%]">TAREA</TableColumn>
                    <TableColumn className="">DESCRIPCIÓN</TableColumn>
                    <TableColumn className="w-[15%] max-w-[15%]">
                      TIPO
                    </TableColumn>
                    <TableColumn className="w-[15%] max-w-[15%]">
                      FASE/SUBFASE
                    </TableColumn>
                    <TableColumn className="w-[12%] max-w-[12%]">
                      ESTADO
                    </TableColumn>
                    <TableColumn className="w-[8%] max-w-[8%]">
                      ACCIÓN
                    </TableColumn>
                  </TableHeader>
                  <TableBody emptyContent="No hay tareas disponibles">
                    {tasks?.map((task: any) => (
                      <TableRow key={task.field_id}>
                        <TableCell className="font-medium">
                          <Tooltip
                            className="max-w-xs"
                            content={task.field_name}
                            placement="top"
                            showArrow={true}
                          >
                            <div
                              className="truncate max-w-full"
                              title={task.field_name}
                            >
                              {task.field_name}
                            </div>
                          </Tooltip>
                        </TableCell>
                        <TableCell className="">
                          <Tooltip
                            className="max-w-xs"
                            content={task.description}
                            placement="top"
                            showArrow={true}
                          >
                            <div
                              className="truncate cursor-help max-w-full"
                              title={task.description}
                            >
                              {task.description.length > 20
                                ? task.description.slice(0, 20) + "..."
                                : task.description}
                            </div>
                          </Tooltip>
                        </TableCell>
                        <TableCell className="w-[15%] max-w-[15%]">
                          <Chip
                            className="text-xs"
                            color="primary"
                            size="sm"
                            variant="flat"
                          >
                            {task.type === "SELECTION"
                              ? "Selección"
                              : task.type === "TASK"
                                ? "Tarea"
                                : task.type === "DOCUMENT"
                                  ? "Documento"
                                  : task.type === "TASK_WITH_SUBTASKS"
                                    ? "Subtarea"
                                    : task.type === "INFORMATIVE"
                                      ? "Informativo"
                                      : task.type}
                          </Chip>
                        </TableCell>
                        <TableCell className="w-[15%] max-w-[15%]">
                          <div className="text-xs max-w-full">
                            <div className="font-medium truncate">
                              {task.phase_name === "INCUBADORA"
                                ? "TAKE OFF"
                                : task.phase_name}
                            </div>
                            {task.subphase_name && (
                              <div className="text-default-500 truncate">
                                {task.subphase_name}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="w-[12%] max-w-[12%]">
                          <Chip
                            className="text-xs"
                            color={getStatusColor(task.status)}
                            size="sm"
                            variant="flat"
                          >
                            {task.status === "IN_PROGRESS"
                              ? "En progreso"
                              : "No completado"}
                          </Chip>
                        </TableCell>
                        <TableCell className="w-[8%] max-w-[8%]">
                          <Button
                            isIconOnly
                            color="primary"
                            size="sm"
                            title="Ver tarea"
                            variant="flat"
                            onPress={() => onViewTask(task.field_id)}
                          >
                            <Icon
                              className="w-4 h-4"
                              icon="lucide:arrow-right"
                            />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Cerrar
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
