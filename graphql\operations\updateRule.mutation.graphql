mutation ExecuteUpdateRule(
  $id: ID!
  $name: String
  $description: String
  $status: String
  $value: String
  $action: String
  $condition: String
  $originFieldId: ID
  $targetFieldId: ID
) {
  updateRule(
    id: $id
    name: $name
    description: $description
    status: $status
    value: $value
    action: $action
    condition: $condition
    originFieldId: $originFieldId
    targetFieldId: $targetFieldId
  ) {
    rule {
      id
      name
      description
      action
      condition
      status
      value
      originField {
        id
        name
        selectionOptions
      }
      targetField {
        id
        name
        selectionOptions
      }
    }
  }
}